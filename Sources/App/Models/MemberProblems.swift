//
//  MemberProblems.swift
//  
//
//  Created by Augment Agent on 6/22/25.
//

import Foundation
import Fluent
import Vapor

// Pivot model for many-to-many relationship between Member and Problem
final class MemberProblems: Model, @unchecked Sendable {
    static let schema = "member_problems"
    
    @ID var id: UUID?
    
    @Parent(key: "member_id")
    var member: Member
    
    @Parent(key: "problem_id")
    var problem: Problem
    
    // Additional fields for the relationship
    @Field(key: "assigned_date") var assignedDate: Date
    @OptionalField(key: "assigned_by") var assignedBy: String? // Who assigned this problem to the member
    @OptionalField(key: "notes") var notes: String? // Any notes about this specific assignment
    @Field(key: "is_active") var isActive: Bool // Whether this assignment is currently active
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, member: Member, problem: Problem, assignedBy: String? = nil, notes: String? = nil) throws {
        self.id = id
        self.$member.id = try member.requireID()
        self.$problem.id = try problem.requireID()
        self.assignedDate = Date()
        self.assignedBy = assignedBy
        self.notes = notes
        self.isActive = true
    }
}

// MARK: - Content Conformance
extension MemberProblems: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case assignedDate = "assigned_date"
        case assignedBy = "assigned_by"
        case notes
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case member
        case problem
    }
}

// MARK: - Migration
struct CreateMemberProblems: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(MemberProblems.schema)
            .id()
            .field("member_id", .uuid, .required, .references(Member.schema, "id", onDelete: .cascade))
            .field("problem_id", .uuid, .required, .references(Problem.schema, "id", onDelete: .cascade))
            .field("assigned_date", .date, .required)
            .field("assigned_by", .string)
            .field("notes", .string)
            .field("is_active", .bool, .required, .sql(.default(true)))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .unique(on: "member_id", "problem_id") // Prevent duplicate assignments
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(MemberProblems.schema).delete()
    }
}

// MARK: - Request/Response DTOs
struct MemberProblemAssignRequest: Content {
    let problemId: UUID
    let assignedBy: String?
    let notes: String?
}

struct MemberProblemUpdateRequest: Content {
    let assignedBy: String?
    let notes: String?
    let isActive: Bool?
}

struct MemberProblemResponse: Content {
    let id: UUID?
    let assignedDate: Date
    let assignedBy: String?
    let notes: String?
    let isActive: Bool
    let createdAt: Date?
    let updatedAt: Date?
    let problem: ProblemResponse
    
    init(memberProblem: MemberProblems, problem: Problem) {
        self.id = memberProblem.id
        self.assignedDate = memberProblem.assignedDate
        self.assignedBy = memberProblem.assignedBy
        self.notes = memberProblem.notes
        self.isActive = memberProblem.isActive
        self.createdAt = memberProblem.createdAt
        self.updatedAt = memberProblem.updatedAt
        self.problem = ProblemResponse(
            id: problem.id,
            icdCode: problem.icdCode,
            description: problem.description,
            clinicalNote: problem.clinicalNote,
            status: problem.status,
            dateIdentified: problem.dateIdentified,
            source: problem.source,
            confirmedBy: problem.confirmedBy,
            createdAt: problem.createdAt,
            updatedAt: problem.updatedAt
        )
    }
}

struct ProblemResponse: Content {
    let id: UUID?
    let icdCode: String?
    let description: String
    let clinicalNote: String?
    let status: String
    let dateIdentified: Date
    let source: String
    let confirmedBy: String?
    let createdAt: Date?
    let updatedAt: Date?
}
