//
//  MemberProblemsController.swift
//  
//
//  Created by Augment Agent on 6/22/25.
//

import Foundation
import Fluent
import Vapor

struct MemberProblemsController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let memberProblems = routes.grouped("api", "members", ":memberID", "problems")
        
        // Member problem management endpoints
        memberProblems.get(use: listMemberProblems)
        memberProblems.post(use: assignProblemToMember)
        memberProblems.get(":problemAssignmentID", use: getMemberProblem)
        memberProblems.put(":problemAssignmentID", use: updateMemberProblem)
        memberProblems.delete(":problemAssignmentID", use: removeProblemFromMember)
        
        // Bulk operations
        memberProblems.post("bulk", use: assignMultipleProblems)
        memberProblems.put("bulk", "deactivate", use: deactivateMultipleProblems)
        
        // Problem search and filtering
        memberProblems.get("active", use: listActiveMemberProblems)
        memberProblems.get("inactive", use: listInactiveMemberProblems)
        memberProblems.get("search", use: searchMemberProblems)
    }
    
    // MARK: - List Member Problems
    func listMemberProblems(req: Request) throws -> EventLoopFuture<[MemberProblemResponse]> {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        return MemberProblems.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .with(\.$problem)
            .sort(\.$assignedDate, .descending)
            .all()
            .map { memberProblems in
                return memberProblems.map { memberProblem in
                    MemberProblemResponse(memberProblem: memberProblem, problem: memberProblem.problem)
                }
            }
    }
    
    // MARK: - List Active Member Problems
    func listActiveMemberProblems(req: Request) throws -> EventLoopFuture<[MemberProblemResponse]> {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        return MemberProblems.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .filter(\.$isActive == true)
            .with(\.$problem)
            .sort(\.$assignedDate, .descending)
            .all()
            .map { memberProblems in
                return memberProblems.map { memberProblem in
                    MemberProblemResponse(memberProblem: memberProblem, problem: memberProblem.problem)
                }
            }
    }
    
    // MARK: - List Inactive Member Problems
    func listInactiveMemberProblems(req: Request) throws -> EventLoopFuture<[MemberProblemResponse]> {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        return MemberProblems.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .filter(\.$isActive == false)
            .with(\.$problem)
            .sort(\.$assignedDate, .descending)
            .all()
            .map { memberProblems in
                return memberProblems.map { memberProblem in
                    MemberProblemResponse(memberProblem: memberProblem, problem: memberProblem.problem)
                }
            }
    }
    
    // MARK: - Assign Problem to Member
    func assignProblemToMember(req: Request) throws -> EventLoopFuture<MemberProblemResponse> {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        let assignRequest = try req.content.decode(MemberProblemAssignRequest.self)
        
        // Check if member exists
        return Member.find(memberID, on: req.db)
            .unwrap(or: Abort(.notFound, reason: "Member not found"))
            .flatMap { member in
                // Check if problem exists
                return Problem.find(assignRequest.problemId, on: req.db)
                    .unwrap(or: Abort(.notFound, reason: "Problem not found"))
                    .flatMap { problem in
                        // Check if assignment already exists
                        return MemberProblems.query(on: req.db)
                            .filter(\.$member.$id == memberID)
                            .filter(\.$problem.$id == assignRequest.problemId)
                            .first()
                            .flatMap { existingAssignment in
                                if let existing = existingAssignment {
                                    // Update existing assignment to active
                                    existing.isActive = true
                                    existing.assignedBy = assignRequest.assignedBy
                                    existing.notes = assignRequest.notes
                                    existing.assignedDate = Date()
                                    
                                    return existing.save(on: req.db)
                                        .map { MemberProblemResponse(memberProblem: existing, problem: problem) }
                                } else {
                                    // Create new assignment
                                    do {
                                        let memberProblem = try MemberProblems(
                                            member: member,
                                            problem: problem,
                                            assignedBy: assignRequest.assignedBy,
                                            notes: assignRequest.notes
                                        )
                                        
                                        return memberProblem.save(on: req.db)
                                            .map { MemberProblemResponse(memberProblem: memberProblem, problem: problem) }
                                    } catch {
                                        return req.eventLoop.makeFailedFuture(error)
                                    }
                                }
                            }
                    }
            }
    }
    
    // MARK: - Get Specific Member Problem
    func getMemberProblem(req: Request) throws -> EventLoopFuture<MemberProblemResponse> {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        guard let problemAssignmentID = req.parameters.get("problemAssignmentID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid problem assignment ID")
        }
        
        return MemberProblems.query(on: req.db)
            .filter(\.$id == problemAssignmentID)
            .filter(\.$member.$id == memberID)
            .with(\.$problem)
            .first()
            .unwrap(or: Abort(.notFound, reason: "Problem assignment not found"))
            .map { memberProblem in
                MemberProblemResponse(memberProblem: memberProblem, problem: memberProblem.problem)
            }
    }
    
    // MARK: - Update Member Problem
    func updateMemberProblem(req: Request) throws -> EventLoopFuture<MemberProblemResponse> {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        guard let problemAssignmentID = req.parameters.get("problemAssignmentID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid problem assignment ID")
        }
        
        let updateRequest = try req.content.decode(MemberProblemUpdateRequest.self)
        
        return MemberProblems.query(on: req.db)
            .filter(\.$id == problemAssignmentID)
            .filter(\.$member.$id == memberID)
            .with(\.$problem)
            .first()
            .unwrap(or: Abort(.notFound, reason: "Problem assignment not found"))
            .flatMap { memberProblem in
                // Update fields if provided
                if let assignedBy = updateRequest.assignedBy {
                    memberProblem.assignedBy = assignedBy
                }
                if let notes = updateRequest.notes {
                    memberProblem.notes = notes
                }
                if let isActive = updateRequest.isActive {
                    memberProblem.isActive = isActive
                }
                
                return memberProblem.save(on: req.db)
                    .map { MemberProblemResponse(memberProblem: memberProblem, problem: memberProblem.problem) }
            }
    }
    
    // MARK: - Remove Problem from Member
    func removeProblemFromMember(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        guard let problemAssignmentID = req.parameters.get("problemAssignmentID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid problem assignment ID")
        }
        
        return MemberProblems.query(on: req.db)
            .filter(\.$id == problemAssignmentID)
            .filter(\.$member.$id == memberID)
            .first()
            .unwrap(or: Abort(.notFound, reason: "Problem assignment not found"))
            .flatMap { memberProblem in
                return memberProblem.delete(on: req.db)
                    .transform(to: .noContent)
            }
    }
    
    // MARK: - Assign Multiple Problems
    func assignMultipleProblems(req: Request) throws -> EventLoopFuture<[MemberProblemResponse]> {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        let assignRequests = try req.content.decode([MemberProblemAssignRequest].self)
        
        return Member.find(memberID, on: req.db)
            .unwrap(or: Abort(.notFound, reason: "Member not found"))
            .flatMap { member in
                let assignmentFutures = assignRequests.map { assignRequest in
                    return Problem.find(assignRequest.problemId, on: req.db)
                        .unwrap(or: Abort(.notFound, reason: "Problem not found"))
                        .flatMap { problem in
                            do {
                                let memberProblem = try MemberProblems(
                                    member: member,
                                    problem: problem,
                                    assignedBy: assignRequest.assignedBy,
                                    notes: assignRequest.notes
                                )
                                
                                return memberProblem.save(on: req.db)
                                    .map { MemberProblemResponse(memberProblem: memberProblem, problem: problem) }
                            } catch {
                                return req.eventLoop.makeFailedFuture(error)
                            }
                        }
                }
                
                return EventLoopFuture.whenAllSucceed(assignmentFutures, on: req.eventLoop)
            }
    }
    
    // MARK: - Deactivate Multiple Problems
    func deactivateMultipleProblems(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        let problemAssignmentIDs = try req.content.decode([UUID].self)
        
        return MemberProblems.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .filter(\.$id ~~ problemAssignmentIDs)
            .all()
            .flatMap { memberProblems in
                let updateFutures = memberProblems.map { memberProblem in
                    memberProblem.isActive = false
                    return memberProblem.save(on: req.db)
                }
                
                return EventLoopFuture.whenAllSucceed(updateFutures, on: req.eventLoop)
                    .transform(to: .ok)
            }
    }
    
    // MARK: - Search Member Problems
    func searchMemberProblems(req: Request) throws -> EventLoopFuture<[MemberProblemResponse]> {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        let searchTerm: String? = req.query["q"]
        let status: String? = req.query["status"]
        let icdCode: String? = req.query["icd_code"]
        
        var query = MemberProblems.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .with(\.$problem)
        
        // Apply filters
        if let status = status {
            if status.lowercased() == "active" {
                query = query.filter(\.$isActive == true)
            } else if status.lowercased() == "inactive" {
                query = query.filter(\.$isActive == false)
            }
        }
        
        if let searchTerm = searchTerm, !searchTerm.isEmpty {
            query = query.join(Problem.self, on: \MemberProblems.$problem.$id == \Problem.$id)
                .group(.or) { group in
                    group.filter(Problem.self, \.$description ~~ searchTerm)
                    group.filter(Problem.self, \.$clinicalNote ~~ searchTerm)
                }
        }
        
        if let icdCode = icdCode, !icdCode.isEmpty {
            query = query.join(Problem.self, on: \MemberProblems.$problem.$id == \Problem.$id)
                .filter(Problem.self, \.$icdCode == icdCode)
        }
        
        return query.sort(\.$assignedDate, .descending)
            .all()
            .map { memberProblems in
                return memberProblems.map { memberProblem in
                    MemberProblemResponse(memberProblem: memberProblem, problem: memberProblem.problem)
                }
            }
    }
}
