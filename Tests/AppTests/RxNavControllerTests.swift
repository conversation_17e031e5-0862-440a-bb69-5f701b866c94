import XCTest
import Vapor
@testable import App

final class RxNavControllerTests: XCTestCase {
    var app: Application!
    
    override func setUp() async throws {
        app = Application(.testing)
        try await configure(app)
    }
    
    override func tearDown() async throws {
        app.shutdown()
    }
    
    func testHealthCheck() async throws {
        try app.test(.GET, "rxnav/health") { res in
            XCTAssertEqual(res.status, .ok)
            let response = try res.content.decode([String: String].self)
            XCTAssertEqual(response["service"], "RxNav API")
            XCTAssertNotNil(response["status"])
        }
    }
    
    func testSearchMedications() async throws {
        let searchRequest = MedicationSearchRequest(name: "lisinopril", includeDetails: false)
        
        try app.test(.POST, "rxnav/search", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            XCTAssertEqual(res.status, .ok)
            let response = try res.content.decode(MedicationSearchResponse.self)
            XCTAssertEqual(response.searchTerm, "lisinopril")
            XCTAssertGreaterThan(response.totalResults, 0)
            XCTAssertGreaterThan(response.medications.count, 0)
            
            // Check that we have both generic and branded medications
            let hasGeneric = response.medications.contains { $0.isGeneric }
            let hasBranded = response.medications.contains { !$0.isGeneric }
            XCTAssertTrue(hasGeneric || hasBranded)
            
            // Verify medication structure
            let firstMedication = response.medications.first!
            XCTAssertFalse(firstMedication.rxcui.isEmpty)
            XCTAssertFalse(firstMedication.fullName.isEmpty)
            XCTAssertFalse(firstMedication.displayName.isEmpty)
        }
    }
    
    func testSearchMedicationsWithDetails() async throws {
        let searchRequest = MedicationSearchRequest(name: "aspirin", includeDetails: true)
        
        try app.test(.POST, "rxnav/search", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            XCTAssertEqual(res.status, .ok)
            let response = try res.content.decode(MedicationSearchResponse.self)
            XCTAssertEqual(response.searchTerm, "aspirin")
            XCTAssertGreaterThan(response.totalResults, 0)
            
            // Check that detailed information is included
            if let firstMedication = response.medications.first {
                // Should have enhanced properties when includeDetails is true
                XCTAssertNotNil(firstMedication.isPrescribable)
            }
        }
    }
    
    func testSearchByBrandName() async throws {
        let searchRequest = MedicationSearchRequest(name: "Lipitor", includeDetails: false)
        
        try app.test(.POST, "rxnav/search/brand", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            XCTAssertEqual(res.status, .ok)
            let response = try res.content.decode(MedicationSearchResponse.self)
            XCTAssertEqual(response.searchTerm, "Lipitor")
            
            // All results should be branded medications
            for medication in response.medications {
                XCTAssertFalse(medication.isGeneric, "Brand search should only return branded medications")
            }
        }
    }
    
    func testSearchByIngredient() async throws {
        let searchRequest = MedicationSearchRequest(name: "atorvastatin", includeDetails: false)
        
        try app.test(.POST, "rxnav/search/ingredient", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            XCTAssertEqual(res.status, .ok)
            let response = try res.content.decode(MedicationSearchResponse.self)
            XCTAssertEqual(response.searchTerm, "atorvastatin")
            
            // All results should be generic medications
            for medication in response.medications {
                XCTAssertTrue(medication.isGeneric, "Ingredient search should only return generic medications")
            }
        }
    }
    
    func testGetMedicationDetails() async throws {
        // First search for a medication to get an RXCUI
        let searchRequest = MedicationSearchRequest(name: "lisinopril", includeDetails: false)
        
        try app.test(.POST, "rxnav/search", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { searchRes in
            XCTAssertEqual(searchRes.status, .ok)
            let searchResponse = try searchRes.content.decode(MedicationSearchResponse.self)
            
            guard let firstMedication = searchResponse.medications.first else {
                XCTFail("No medications found in search")
                return
            }
            
            // Now get detailed information for this medication
            let detailRequest = MedicationDetailRequest(rxcui: firstMedication.rxcui)
            
            try app.test(.POST, "rxnav/details", beforeRequest: { req in
                try req.content.encode(detailRequest)
            }) { detailRes in
                XCTAssertEqual(detailRes.status, .ok)
                let detailResponse = try detailRes.content.decode(MedicationDetailResponse.self)
                
                XCTAssertEqual(detailResponse.medication.rxcui, firstMedication.rxcui)
                XCTAssertFalse(detailResponse.medication.fullName.isEmpty)
                XCTAssertGreaterThan(detailResponse.medication.properties.count, 0)
                XCTAssertGreaterThan(detailResponse.medication.codes.count, 0)
            }
        }
    }
    
    func testSpellingSuggestions() async throws {
        let searchRequest = MedicationSearchRequest(name: "lisinpril", includeDetails: false) // Intentional typo
        
        try app.test(.POST, "rxnav/search/spelling", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            XCTAssertEqual(res.status, .ok)
            let response = try res.content.decode([String: [String]].self)
            XCTAssertNotNil(response["suggestions"])
            
            // Should suggest "lisinopril" for the typo "lisinpril"
            if let suggestions = response["suggestions"] {
                let hasCorrectSuggestion = suggestions.contains { $0.lowercased().contains("lisinopril") }
                XCTAssertTrue(hasCorrectSuggestion, "Should suggest correct spelling")
            }
        }
    }
    
    func testApproximateSearch() async throws {
        let searchRequest = MedicationSearchRequest(name: "lisino", includeDetails: false) // Partial name
        
        try app.test(.POST, "rxnav/search/approximate", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            XCTAssertEqual(res.status, .ok)
            let response = try res.content.decode(MedicationSearchResponse.self)
            XCTAssertEqual(response.searchTerm, "lisino")
            
            // Should find medications that approximately match
            for medication in response.medications {
                XCTAssertFalse(medication.rxcui.isEmpty)
                XCTAssertFalse(medication.fullName.isEmpty)
            }
        }
    }
    
    func testMedicationInfoParsing() {
        // Test the utility methods for parsing medication information
        let controller = RxNavController()
        
        // Test strength extraction
        XCTAssertEqual(controller.extractStrength(from: "lisinopril 10 MG Oral Tablet"), "10 MG")
        XCTAssertEqual(controller.extractStrength(from: "aspirin 325 MG Oral Tablet"), "325 MG")
        XCTAssertNil(controller.extractStrength(from: "lisinopril Oral Tablet"))
        
        // Test dose form extraction
        XCTAssertEqual(controller.extractDoseForm(from: "lisinopril 10 MG Oral Tablet"), "Oral Tablet")
        XCTAssertEqual(controller.extractDoseForm(from: "insulin injection"), "Injection")
        
        // Test route extraction
        XCTAssertEqual(controller.extractRoute(from: "lisinopril 10 MG Oral Tablet"), "Oral")
        XCTAssertEqual(controller.extractRoute(from: "insulin Topical Cream"), "Topical")
        
        // Test brand name extraction
        XCTAssertEqual(controller.extractBrandName(from: "lisinopril 10 MG Oral Tablet [Zestril]", tty: "SBD"), "Zestril")
        XCTAssertNil(controller.extractBrandName(from: "lisinopril 10 MG Oral Tablet", tty: "SCD"))
        
        // Test generic medication detection
        XCTAssertTrue(controller.isGenericMedication(tty: "SCD"))
        XCTAssertTrue(controller.isGenericMedication(tty: "GPCK"))
        XCTAssertFalse(controller.isGenericMedication(tty: "SBD"))
        XCTAssertFalse(controller.isGenericMedication(tty: "BPCK"))
    }
}
