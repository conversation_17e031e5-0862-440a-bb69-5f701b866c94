{"info": {"_postman_id": "member-diagnoses-medications-collection", "name": "HMBL Core - Member Diagnoses, Medications, Problems, WHO ICD & RxNav API", "description": "Complete Postman collection for Member Diagnoses, Medications, and Problems controllers with WHO ICD API and RxNav API integration. Includes endpoints for managing member problems, searching ICD-10 and ICD-11 classifications, retrieving detailed disease information, searching medications by name/brand/ingredient, getting medication details, and creating diagnoses with standardized medical codes.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "4.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "memberID", "value": "123e4567-e89b-12d3-a456-426614174000", "type": "string"}, {"key": "diagnosisID", "value": "123e4567-e89b-12d3-a456-426614174001", "type": "string"}, {"key": "medicationID", "value": "123e4567-e89b-12d3-a456-426614174002", "type": "string"}, {"key": "authToken", "value": "your-jwt-token-here", "type": "string"}, {"key": "whoEntityId", "value": "142601234", "type": "string"}, {"key": "rxcui", "value": "617314", "type": "string"}, {"key": "medicationName", "value": "lisinopril", "type": "string"}, {"key": "brandName", "value": "<PERSON><PERSON><PERSON>", "type": "string"}, {"key": "ingredientName", "value": "atorvastatin", "type": "string"}, {"key": "problemID", "value": "987fcdeb-51a2-4567-8901-234567890abc", "type": "string"}, {"key": "problemAssignmentID", "value": "456789ab-cdef-1234-5678-90abcdef1234", "type": "string"}], "item": [{"name": "WHO ICD API", "description": "WHO International Classification of Diseases API endpoints for searching diagnoses and disease classifications", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/whoicd/health", "host": ["{{baseUrl}}"], "path": ["whoicd", "health"]}, "description": "Check the health status of the WHO ICD API integration"}}, {"name": "Search Diagnoses (General)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"diabetes mellitus\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search", "host": ["{{baseUrl}}"], "path": ["whoicd", "search"]}, "description": "Search for diagnoses across the default ICD release (ICD-11 2024-01)"}}, {"name": "Search ICD-10 Diagnoses", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"hypertension\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/icd10", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "icd10"]}, "description": "Search specifically within ICD-10 classification (release 2019-04)"}}, {"name": "Search ICD-11 Diagnoses", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"covid-19\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/icd11", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "icd11"]}, "description": "Search specifically within ICD-11 classification (release 2024-01)"}}, {"name": "Combined Search (ICD-10 & ICD-11)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"pneumonia\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/combined", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "combined"]}, "description": "Search across both ICD-10 and ICD-11 classifications simultaneously"}}, {"name": "Get Entity Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/whoicd/entity/{{whoEntityId}}", "host": ["{{baseUrl}}"], "path": ["whoicd", "entity", "{{whoEntityId}}"]}, "description": "Retrieve detailed information about a specific ICD entity"}}, {"name": "Search with Detailed Information", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"diabetes\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/detailed", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "detailed"]}, "description": "Search for diagnoses and automatically fetch detailed information for each result including id, title, score, theCode, definition, synonyms, and browserUrl"}}, {"name": "Test Authentication Token", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/whoicd/token/test", "host": ["{{baseUrl}}"], "path": ["whoicd", "token", "test"]}, "description": "Test the WHO ICD API authentication (for debugging purposes)"}}]}, {"name": "WHO ICD Examples", "description": "Common medical condition search examples using WHO ICD API", "item": [{"name": "Search - Heart Conditions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"heart failure\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/combined", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "combined"]}}}, {"name": "Search - Respiratory Conditions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"asthma\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search", "host": ["{{baseUrl}}"], "path": ["whoicd", "search"]}}}, {"name": "Search - Mental Health", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"depression\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/icd11", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "icd11"]}}}, {"name": "Search - Infectious Diseases", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"influenza\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/combined", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "combined"]}}}, {"name": "Search - Cancer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"breast cancer\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search", "host": ["{{baseUrl}}"], "path": ["whoicd", "search"]}}}]}, {"name": "RxNav API", "description": "RxNav API endpoints for searching medications, getting medication details, and finding drug information", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/rxnav/health", "host": ["{{baseUrl}}"], "path": ["rxnav", "health"]}, "description": "Check the health status of the RxNav API integration"}}, {"name": "Search Medications (Basic)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"{{<PERSON><PERSON><PERSON>}}\",\n  \"includeDetails\": false\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search", "host": ["{{baseUrl}}"], "path": ["rxnav", "search"]}, "description": "Search for medications by name. Returns both generic and branded medications."}}, {"name": "Search Medications (With Details)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"aspirin\",\n  \"includeDetails\": true\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search", "host": ["{{baseUrl}}"], "path": ["rxnav", "search"]}, "description": "Search for medications with enhanced details including prescribability, schedule, and human drug classification."}}, {"name": "Search by Brand Name", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"{{brandName}}\",\n  \"includeDetails\": false\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search/brand", "host": ["{{baseUrl}}"], "path": ["rxnav", "search", "brand"]}, "description": "Search for medications by brand name. Returns only branded medications (isGeneric: false)."}}, {"name": "Search by Ingredient", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"{{ingredientName}}\",\n  \"includeDetails\": false\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search/ingredient", "host": ["{{baseUrl}}"], "path": ["rxnav", "search", "ingredient"]}, "description": "Search for medications by active ingredient. Returns only generic medications (isGeneric: true)."}}, {"name": "Get Medication Details", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"rxcui\": \"{{rxcui}}\"\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/details", "host": ["{{baseUrl}}"], "path": ["rxnav", "details"]}, "description": "Get comprehensive details for a specific medication using its RXCUI. Returns properties, codes, and sources."}}, {"name": "Spelling Suggestions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"lisinpril\",\n  \"includeDetails\": false\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search/spelling", "host": ["{{baseUrl}}"], "path": ["rxnav", "search", "spelling"]}, "description": "Get spelling suggestions for medication names. Useful for handling typos in medication searches."}}, {"name": "Approximate Search", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"lisino\",\n  \"includeDetails\": false\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search/approximate", "host": ["{{baseUrl}}"], "path": ["rxnav", "search", "approximate"]}, "description": "Find medications with partial name matches. Useful for autocomplete functionality."}}]}, {"name": "RxNav Examples", "description": "Common medication search examples using RxNav API", "item": [{"name": "Search - Blood Pressure Medications", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"lisinopril\",\n  \"includeDetails\": true\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search", "host": ["{{baseUrl}}"], "path": ["rxnav", "search"]}, "description": "Search for ACE inhibitor medications with detailed information"}}, {"name": "Search - Cholesterol Medications (Brand)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON>\",\n  \"includeDetails\": false\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search/brand", "host": ["{{baseUrl}}"], "path": ["rxnav", "search", "brand"]}, "description": "Search for branded statin medications"}}, {"name": "Search - Pain Medications (Generic)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"ibuprofen\",\n  \"includeDetails\": false\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search/ingredient", "host": ["{{baseUrl}}"], "path": ["rxnav", "search", "ingredient"]}, "description": "Search for generic NSAID medications"}}, {"name": "Search - Diabetes Medications", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"metformin\",\n  \"includeDetails\": true\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search", "host": ["{{baseUrl}}"], "path": ["rxnav", "search"]}, "description": "Search for diabetes medications with detailed properties"}}, {"name": "Get Details - Specific Medication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"rxcui\": \"617314\"\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/details", "host": ["{{baseUrl}}"], "path": ["rxnav", "details"]}, "description": "Get comprehensive details for Lipitor 10 MG Oral Tablet (RXCUI: 617314)"}}]}, {"name": "Member Diagnoses", "item": [{"name": "Create Diagnosis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"BP remains elevated despite medication adjustment. Patient reports compliance with current regimen.\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2023-06-01T00:00:00Z\",\n  \"source\": \"EHR import\",\n  \"confirmedBy\": \"Dr<PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "Create a new diagnosis for a member. Use WHO ICD API to search for appropriate ICD codes first."}}, {"name": "Create Diagnosis (WHO ICD Example)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"5A11\",\n  \"description\": \"Type 2 diabetes mellitus\",\n  \"clinicalNote\": \"Diagnosed based on elevated HbA1c (8.2%) and fasting glucose levels. Patient has family history of diabetes.\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2023-06-15T00:00:00Z\",\n  \"source\": \"WHO ICD search\",\n  \"confirmedBy\": \"<PERSON><PERSON>, MD\",\n  \"whoEntityId\": \"142601234\",\n  \"whoEntityUrl\": \"https://icd.who.int/browse11/l-m/en#/http://id.who.int/icd/entity/142601234\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "Example of creating a diagnosis using ICD-11 code obtained from WHO ICD API search"}}, {"name": "List Member Diagnoses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}}}, {"name": "Get Diagnosis", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "diagnoses", "{{diagnosisID}}"]}}}, {"name": "Update Diagnosis", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"BP now controlled with current medication regimen. Patient showing excellent compliance.\",\n  \"status\": \"resolved\",\n  \"dateIdentified\": \"2023-06-01T00:00:00Z\",\n  \"source\": \"EHR import\",\n  \"confirmedBy\": \"Dr<PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "diagnoses", "{{diagnosisID}}"]}}}, {"name": "Delete Diagnosis", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "diagnoses", "{{diagnosisID}}"]}}}]}, {"name": "Diagnosis Workflow (WHO ICD Integration)", "description": "Complete workflow demonstrating how to search WHO ICD API and create diagnoses", "item": [{"name": "Step 1: Search WHO ICD for Condition", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"type 2 diabetes\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/combined", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "combined"]}, "description": "Search for diabetes in both ICD-10 and ICD-11 to find appropriate codes"}}, {"name": "Step 2: Get Detailed Entity Information", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/whoicd/entity/{{whoEntityId}}", "host": ["{{baseUrl}}"], "path": ["whoicd", "entity", "{{whoEntityId}}"]}, "description": "Get detailed information about the selected ICD entity (use ID from search results)"}}, {"name": "Step 3: Create Diagnosis with WHO ICD Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"5A11\",\n  \"description\": \"Type 2 diabetes mellitus\",\n  \"clinicalNote\": \"Pat<PERSON> presents with elevated HbA1c (8.5%) and classic symptoms. Family history positive for diabetes. Confirmed diagnosis using WHO ICD-11 classification.\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2023-06-20T00:00:00Z\",\n  \"source\": \"WHO ICD search\",\n  \"confirmedBy\": \"<PERSON><PERSON>, <PERSON>\",\n  \"whoEntityId\": \"142601234\",\n  \"whoEntityUrl\": \"https://icd.who.int/browse11/l-m/en#/http://id.who.int/icd/entity/142601234\",\n  \"icdVersion\": \"ICD-11\",\n  \"releaseId\": \"2024-01\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "Create the diagnosis using the ICD code and information obtained from WHO ICD API"}}, {"name": "Step 4: <PERSON><PERSON>fy Created Diagnosis", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "List all diagnoses for the member to verify the new diagnosis was created"}}]}, {"name": "Medication Workflow (RxNav Integration)", "description": "Complete workflow demonstrating how to search RxNav API and create medications", "item": [{"name": "Step 1: Search RxNav for Medication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"lisinopril\",\n  \"includeDetails\": true\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search", "host": ["{{baseUrl}}"], "path": ["rxnav", "search"]}, "description": "Search for medications to find appropriate RXCUI and medication details"}}, {"name": "Step 2: Get Detailed Medication Information", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"rxcui\": \"314076\"\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/details", "host": ["{{baseUrl}}"], "path": ["rxnav", "details"]}, "description": "Get comprehensive details about the selected medication (use RXCUI from search results)"}}, {"name": "Step 3: Create Medication with RxNav Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"medicationName\": \"lisinopril 10 MG Oral Tablet\",\n  \"rxNormCode\": \"314076\",\n  \"rxcui\": \"314076\",\n  \"dosage\": \"10 MG\",\n  \"route\": \"Oral\",\n  \"doseForm\": \"Tablet\",\n  \"frequency\": \"daily\",\n  \"startDate\": \"2023-06-20T00:00:00Z\",\n  \"prescribedBy\": \"<PERSON>. <PERSON>, <PERSON>\",\n  \"status\": \"active\",\n  \"adherenceNotes\": \"Patient reports good compliance. Confirmed medication details using RxNav API.\",\n  \"source\": \"RxNav search\",\n  \"medicationType\": \"prescribed\",\n  \"isGeneric\": true,\n  \"isPrescribable\": true,\n  \"strength\": \"10 MG\",\n  \"brandName\": null,\n  \"tty\": \"SCD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}, "description": "Create the medication using the RXCUI and detailed information obtained from RxNav API"}}, {"name": "Step 4: Verify Created Medication", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}, "description": "List all medications for the member to verify the new medication was created"}}]}, {"name": "Comprehensive Care Workflow (Problems + Diagnoses + Medications)", "description": "Complete workflow demonstrating integrated problem assignment, diagnosis creation, and medication management", "item": [{"name": "Step 1: Assign Problem from Care Plan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"problemId\": \"{{problemID}}\",\n  \"assignedBy\": \"Dr. <PERSON>, <PERSON>\",\n  \"notes\": \"Patient identified with hypertension during care plan review. BP 150/95 on multiple readings. Requires comprehensive management including medication and lifestyle modifications.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems"]}, "description": "Assign a problem from the care plan to the member's ongoing problem list"}}, {"name": "Step 2: Create Member Diagnosis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"Newly diagnosed hypertension. Patient has no secondary causes identified. Family history positive for cardiovascular disease.\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2025-06-22T00:00:00Z\",\n  \"source\": \"clinical assessment\",\n  \"confirmedBy\": \"<PERSON><PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "Create a formal diagnosis record for the member based on the assigned problem"}}, {"name": "Step 3: Search RxNav for Medication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"lisinopril\",\n  \"includeDetails\": true\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search", "host": ["{{baseUrl}}"], "path": ["rxnav", "search"]}, "description": "Search RxNav API for appropriate medication to treat the diagnosed condition"}}, {"name": "Step 4: Create Medication with Problem Reference", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"medicationName\": \"lisinopril 10 MG Oral Tablet\",\n  \"rxNormCode\": \"314076\",\n  \"rxcui\": \"314076\",\n  \"dosage\": \"10 MG\",\n  \"route\": \"Oral\",\n  \"doseForm\": \"Tablet\",\n  \"frequency\": \"daily\",\n  \"startDate\": \"2025-06-22T00:00:00Z\",\n  \"prescribedBy\": \"Dr. <PERSON>, <PERSON>\",\n  \"status\": \"active\",\n  \"adherenceNotes\": \"Prescribed for hypertension management. Patient counseled on importance of daily compliance. Monitor BP weekly initially.\",\n  \"source\": \"clinical prescription\",\n  \"medicationType\": \"prescribed\",\n  \"isGeneric\": true,\n  \"isPrescribable\": true,\n  \"strength\": \"10 MG\",\n  \"tty\": \"SCD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}, "description": "Create medication record using RxNav data, linked to the problem and diagnosis"}}, {"name": "Step 5: Update Problem with Treatment Plan", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"assignedBy\": \"<PERSON><PERSON> <PERSON>, <PERSON>\",\n  \"notes\": \"Hypertension management initiated. Started lisinopril 10mg daily. Patient education provided on lifestyle modifications including DASH diet and regular exercise. BP monitoring scheduled weekly x 4 weeks, then monthly. Target BP <130/80.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/{{problemAssignmentID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "{{problemAssignmentID}}"]}, "description": "Update the problem assignment with comprehensive treatment plan details"}}, {"name": "Step 6: Verify Complete Care Record", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}"]}, "description": "Retrieve complete member record to verify problems, diagnoses, and medications are properly linked"}}]}, {"name": "Member Medications", "item": [{"name": "Create Medication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"medicationName\": \"Lisinopril 10mg\",\n  \"rxNormCode\": \"197361\",\n  \"dosage\": \"10mg\",\n  \"route\": \"oral\",\n  \"frequency\": \"daily\",\n  \"startDate\": \"2023-05-20T00:00:00Z\",\n  \"prescribedBy\": \"<PERSON><PERSON> <PERSON>\",\n  \"status\": \"active\",\n  \"adherenceNotes\": \"Member reports 90% adherence\",\n  \"source\": \"EHR import\",\n  \"medicationType\": \"prescribed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}, "description": "Create a new medication for a member. Use RxNav API to search for appropriate RXCUI and medication details first."}}, {"name": "Create Medication (RxNav Example)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"medicationName\": \"atorvastatin 20 MG Oral Tablet\",\n  \"rxNormCode\": \"617310\",\n  \"rxcui\": \"617310\",\n  \"dosage\": \"20 MG\",\n  \"route\": \"Oral\",\n  \"doseForm\": \"Tablet\",\n  \"frequency\": \"daily\",\n  \"startDate\": \"2023-06-15T00:00:00Z\",\n  \"prescribedBy\": \"<PERSON><PERSON>, <PERSON>\",\n  \"status\": \"active\",\n  \"adherenceNotes\": \"Patient started on generic statin. Medication details verified using RxNav API.\",\n  \"source\": \"RxNav search\",\n  \"medicationType\": \"prescribed\",\n  \"isGeneric\": true,\n  \"isPrescribable\": true,\n  \"strength\": \"20 MG\",\n  \"brandName\": null,\n  \"tty\": \"SCD\",\n  \"humanDrug\": \"US\",\n  \"schedule\": \"0\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}, "description": "Example of creating a medication using RXCUI and detailed information obtained from RxNav API search"}}, {"name": "List Member Medications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}}}, {"name": "Get Active Medications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/active", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "active"]}}}, {"name": "Get Medications by Type", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/by-type?type=prescribed", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "by-type"], "query": [{"key": "type", "value": "prescribed"}]}}}, {"name": "Get Medication", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "medications", "{{medicationID}}"]}}}, {"name": "Update Medication", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"medicationName\": \"Lisinopril 20mg\",\n  \"rxNormCode\": \"197361\",\n  \"dosage\": \"20mg\",\n  \"route\": \"oral\",\n  \"frequency\": \"daily\",\n  \"startDate\": \"2023-05-20T00:00:00Z\",\n  \"prescribedBy\": \"<PERSON><PERSON> <PERSON>\",\n  \"status\": \"active\",\n  \"adherenceNotes\": \"Dosage increased. Member reports 95% adherence\",\n  \"source\": \"EHR import\",\n  \"medicationType\": \"prescribed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "medications", "{{medicationID}}"]}}}, {"name": "Discontinue Medication", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/{{medicationID}}/discontinue", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "{{medicationID}}", "discontinue"]}}}, {"name": "Delete Medication", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "medications", "{{medicationID}}"]}}}]}, {"name": "Member Problems", "description": "Endpoints for managing member problem assignments - many-to-many relationship between members and problems", "item": [{"name": "List Member Problems", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems"]}, "description": "Get all problems assigned to a specific member with assignment details"}}, {"name": "Assign Problem to Member", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"problemId\": \"{{problemID}}\",\n  \"assignedBy\": \"Dr. <PERSON>, MD\",\n  \"notes\": \"Patient reports ongoing symptoms, needs monitoring\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems"]}, "description": "Assign an existing problem to a member with assignment metadata"}}, {"name": "Get Specific Member Problem", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/{{problemAssignmentID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "{{problemAssignmentID}}"]}, "description": "Get details of a specific problem assignment for a member"}}, {"name": "Update Member Problem Assignment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"assignedBy\": \"<PERSON><PERSON>, <PERSON>\",\n  \"notes\": \"Updated notes: Patient showing improvement with current treatment plan\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/{{problemAssignmentID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "{{problemAssignmentID}}"]}, "description": "Update assignment details for a member's problem"}}, {"name": "Remove Problem from Member", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/{{problemAssignmentID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "{{problemAssignmentID}}"]}, "description": "Remove a problem assignment from a member (deletes the assignment, not the problem itself)"}}, {"name": "List Active Member Problems", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/active", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "active"]}, "description": "Get only active problem assignments for a member"}}, {"name": "List Inactive Member Problems", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/inactive", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "inactive"]}, "description": "Get only inactive problem assignments for a member"}}, {"name": "Search Member Problems", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/search?q=hypertension&status=active&icd_code=I10", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "search"], "query": [{"key": "q", "value": "hypertension", "description": "Search term for problem description or clinical notes"}, {"key": "status", "value": "active", "description": "Filter by assignment status (active/inactive)"}, {"key": "icd_code", "value": "I10", "description": "Filter by ICD code"}]}, "description": "Search and filter member problems by various criteria"}}, {"name": "Bulk Assign Problems", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"problemId\": \"{{problemID}}\",\n    \"assignedBy\": \"<PERSON><PERSON>, <PERSON>\",\n    \"notes\": \"Primary hypertension - needs regular monitoring\"\n  },\n  {\n    \"problemId\": \"987fcdeb-51a2-4567-8901-234567890def\",\n    \"assignedBy\": \"<PERSON><PERSON>, <PERSON>\",\n    \"notes\": \"Type 2 diabetes - dietary counseling recommended\"\n  }\n]"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/bulk", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "bulk"]}, "description": "Assign multiple problems to a member in a single request"}}, {"name": "Bulk Deactivate Problems", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "[\"{{problemAssignmentID}}\", \"456789ab-cdef-1234-5678-90abcdef5678\", \"456789ab-cdef-1234-5678-90abcdef9012\"]"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/bulk/deactivate", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "bulk", "deactivate"]}, "description": "Deactivate multiple problem assignments for a member"}}]}, {"name": "Member Problems Examples", "description": "Real-world examples of member problem management workflows", "item": [{"name": "Assign Chronic Conditions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"problemId\": \"{{problemID}}\",\n  \"assignedBy\": \"Dr. <PERSON>, <PERSON>\",\n  \"notes\": \"Patient has essential hypertension. BP readings consistently >140/90. Started on ACE inhibitor. Requires monthly monitoring and lifestyle counseling.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems"]}, "description": "Example: Assigning hypertension to a member with detailed clinical notes"}}, {"name": "Assign Multiple Comorbidities", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"problemId\": \"11111111-2222-3333-4444-555555555555\",\n    \"assignedBy\": \"Dr<PERSON> <PERSON>, <PERSON>\",\n    \"notes\": \"Type 2 Diabetes Mellitus - HbA1c 8.2%, needs better glycemic control\"\n  },\n  {\n    \"problemId\": \"22222222-3333-4444-5555-666666666666\",\n    \"assignedBy\": \"Dr<PERSON>, <PERSON>\",\n    \"notes\": \"Hyperlipidemia - LDL 165 mg/dL, started on statin therapy\"\n  },\n  {\n    \"problemId\": \"33333333-4444-5555-6666-777777777777\",\n    \"assignedBy\": \"Dr<PERSON> <PERSON>, <PERSON>\",\n    \"notes\": \"Obesity (BMI 32.5) - referred to nutritionist for weight management\"\n  }\n]"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/bulk", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "bulk"]}, "description": "Example: Assigning multiple related conditions (diabetes, hyperlipidemia, obesity) to a member"}}, {"name": "Search Cardiovascular Problems", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/search?q=cardiovascular&status=active", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "search"], "query": [{"key": "q", "value": "cardiovascular", "description": "Search for cardiovascular-related problems"}, {"key": "status", "value": "active", "description": "Only active problems"}]}, "description": "Example: Finding all active cardiovascular problems for a member"}}, {"name": "Search by ICD Code - Diabetes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/search?icd_code=E11", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "search"], "query": [{"key": "icd_code", "value": "E11", "description": "Type 2 diabetes mellitus ICD-10 code"}]}, "description": "Example: Finding diabetes-related problems using ICD-10 code E11"}}, {"name": "Update Problem Status - Resolved", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"assignedBy\": \"<PERSON><PERSON>, <PERSON>\",\n  \"notes\": \"Acute bronchitis resolved after 10-day course of antibiotics. Patient reports no more cough or shortness of breath. Follow-up in 2 weeks if symptoms return.\",\n  \"isActive\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/{{problemAssignmentID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "{{problemAssignmentID}}"]}, "description": "Example: Marking an acute condition as resolved with detailed notes"}}, {"name": "Care Transition - Update Assignment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"assignedBy\": \"<PERSON><PERSON>, MD (Cardiology)\",\n  \"notes\": \"Patient transferred to cardiology for specialized management of heart failure. Echo shows EF 35%. Started on ACE inhibitor and beta-blocker. Requires close monitoring of renal function and electrolytes.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/{{problemAssignmentID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "{{problemAssignmentID}}"]}, "description": "Example: Updating problem assignment when care is transferred to a specialist"}}, {"name": "Mental Health Problem Assignment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"problemId\": \"44444444-5555-6666-7777-888888888888\",\n  \"assignedBy\": \"<PERSON><PERSON>, MD (Psychiatry)\",\n  \"notes\": \"Major depressive disorder, moderate episode. PHQ-9 score: 14. Started on SSRI therapy. Referred to counseling. Safety assessment completed - no current suicidal ideation. Follow-up in 2 weeks.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems"]}, "description": "Example: Assigning a mental health condition with appropriate clinical documentation"}}, {"name": "Preventive Care Problem", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"problemId\": \"55555555-6666-7777-8888-999999999999\",\n  \"assignedBy\": \"<PERSON>, R<PERSON> (Care Coordinator)\",\n  \"notes\": \"Overdue for mammography screening. Last mammogram 3 years ago. Patient scheduled for screening next week. Discussed importance of regular screening given family history of breast cancer.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems"]}, "description": "Example: Assigning a preventive care problem by a care coordinator"}}]}]}